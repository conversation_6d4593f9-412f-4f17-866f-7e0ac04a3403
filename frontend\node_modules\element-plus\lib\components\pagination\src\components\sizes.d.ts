import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
import type Sizes from './sizes.vue';
export declare const paginationSizesProps: {
    readonly pageSize: {
        readonly type: import("vue").PropType<number>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly pageSizes: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => number[]) | (() => number[]) | ((new (...args: any[]) => number[]) | (() => number[]))[], unknown, unknown, () => [10, 20, 30, 40, 50, 100], boolean>;
    readonly popperClass: {
        readonly type: import("vue").PropType<string>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly disabled: BooleanConstructor;
    readonly teleported: BooleanConstructor;
    readonly size: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "small" | "default" | "large", unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly appendSizeTo: StringConstructor;
};
export type PaginationSizesProps = ExtractPropTypes<typeof paginationSizesProps>;
export type PaginationSizesPropsPublic = __ExtractPublicPropTypes<typeof paginationSizesProps>;
export type SizesInstance = InstanceType<typeof Sizes> & unknown;
