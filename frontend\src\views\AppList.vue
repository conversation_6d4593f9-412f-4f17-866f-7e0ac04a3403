<template>
  <div class="app-list-container">
    <!-- 头部导航 -->
    <el-header class="header">
      <div class="header-content">
        <h1 class="title">
          <el-icon><Robot /></el-icon>
          AI程序管理器
        </h1>
        <el-button type="primary" @click="goToManagement">
          <el-icon><Setting /></el-icon>
          应用管理
        </el-button>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-main class="main-content">
      <div class="content-wrapper">
        <!-- 搜索栏 -->
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索AI应用..."
            class="search-input"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 应用卡片网格 -->
        <div class="apps-grid" v-loading="loading">
          <el-empty v-if="filteredApps.length === 0 && !loading" description="暂无AI应用" />
          
          <div class="app-card" v-for="app in filteredApps" :key="app.id" @click="openChat(app)">
            <el-card class="app-card-content" shadow="hover">
              <div class="app-icon">
                <el-avatar :size="60" :src="app.icon">
                  <el-icon size="30"><ChatDotRound /></el-icon>
                </el-avatar>
              </div>
              <div class="app-info">
                <h3 class="app-name">{{ app.name }}</h3>
                <p class="app-description">{{ app.description }}</p>
                <div class="app-meta">
                  <el-tag size="small" type="info">{{ app.category }}</el-tag>
                  <span class="last-used">最后使用: {{ formatTime(app.lastUsed) }}</span>
                </div>
              </div>
              <div class="app-actions">
                <el-button type="primary" size="small" @click.stop="openChat(app)">
                  <el-icon><ChatDotRound /></el-icon>
                  开始对话
                </el-button>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </el-main>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

export default {
  name: 'AppList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const searchKeyword = ref('')
    
    // 模拟应用数据
    const apps = ref([
      {
        id: 1,
        name: '智能助手',
        description: '通用AI助手，可以回答各种问题，协助完成日常任务',
        category: '通用助手',
        icon: '',
        lastUsed: new Date('2024-01-15'),
        difyUrl: 'https://api.dify.ai',
        apiKey: 'app-xxx'
      },
      {
        id: 2,
        name: '代码助手',
        description: '专业的编程助手，帮助编写、调试和优化代码',
        category: '编程工具',
        icon: '',
        lastUsed: new Date('2024-01-14'),
        difyUrl: 'https://api.dify.ai',
        apiKey: 'app-yyy'
      },
      {
        id: 3,
        name: '文档写作',
        description: '专业的文档写作助手，帮助创建高质量的文档内容',
        category: '写作工具',
        icon: '',
        lastUsed: new Date('2024-01-13'),
        difyUrl: 'https://api.dify.ai',
        apiKey: 'app-zzz'
      }
    ])

    // 过滤应用
    const filteredApps = computed(() => {
      if (!searchKeyword.value) return apps.value
      return apps.value.filter(app => 
        app.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        app.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
      )
    })

    // 格式化时间
    const formatTime = (date) => {
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      return date.toLocaleDateString()
    }

    // 打开聊天界面
    const openChat = (app) => {
      router.push(`/chat/${app.id}`)
    }

    // 跳转到应用管理
    const goToManagement = () => {
      router.push('/management')
    }

    // 加载应用列表
    const loadApps = async () => {
      loading.value = true
      try {
        // 这里将来会调用后端API
        await new Promise(resolve => setTimeout(resolve, 1000))
      } catch (error) {
        ElMessage.error('加载应用列表失败')
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      loadApps()
    })

    return {
      loading,
      searchKeyword,
      apps,
      filteredApps,
      formatTime,
      openChat,
      goToManagement
    }
  }
}
</script>

<style scoped>
.app-list-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.main-content {
  padding: 20px;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.search-section {
  margin-bottom: 30px;
}

.search-input {
  max-width: 400px;
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  min-height: 200px;
}

.app-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.app-card:hover {
  transform: translateY(-5px);
}

.app-card-content {
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.app-icon {
  text-align: center;
  margin-bottom: 15px;
}

.app-info {
  text-align: center;
  margin-bottom: 20px;
}

.app-name {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.app-description {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  min-height: 42px;
}

.app-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.app-actions {
  text-align: center;
}
</style>
