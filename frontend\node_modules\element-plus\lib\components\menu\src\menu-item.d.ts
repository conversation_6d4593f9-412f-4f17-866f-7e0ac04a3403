import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
import type { RouteLocationRaw } from 'vue-router';
import type { MenuItemRegistered } from './types';
export declare const menuItemProps: {
    readonly index: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string) | (() => string | null) | ((new (...args: any[]) => string) | (() => string | null))[], unknown, unknown, null, boolean>;
    readonly route: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => RouteLocationRaw & {}) | (() => RouteLocationRaw) | ((new (...args: any[]) => RouteLocationRaw & {}) | (() => RouteLocationRaw))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly disabled: BooleanConstructor;
};
export type MenuItemProps = ExtractPropTypes<typeof menuItemProps>;
export type MenuItemPropsPublic = __ExtractPublicPropTypes<typeof menuItemProps>;
export declare const menuItemEmits: {
    click: (item: MenuItemRegistered) => boolean;
};
export type MenuItemEmits = typeof menuItemEmits;
