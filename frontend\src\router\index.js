import { createRouter, createWebHistory } from 'vue-router'
import AppList from '../views/AppList.vue'
import ChatInterface from '../views/ChatInterface.vue'
import AppManagement from '../views/AppManagement.vue'

const routes = [
  {
    path: '/',
    name: 'AppList',
    component: AppList,
    meta: { title: 'AI应用列表' }
  },
  {
    path: '/chat/:appId',
    name: 'ChatInterface',
    component: ChatInterface,
    meta: { title: '聊天界面' }
  },
  {
    path: '/management',
    name: 'AppManagement',
    component: AppManagement,
    meta: { title: '应用管理' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫，设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title + ' - AI程序管理器'
  }
  next()
})

export default router
