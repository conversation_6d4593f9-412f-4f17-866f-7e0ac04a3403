<template>
  <div class="management-container">
    <!-- 头部 -->
    <el-header class="header">
      <div class="header-content">
        <div class="header-left">
          <el-button type="text" @click="goBack" class="back-btn">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <h1 class="title">
            <el-icon><Setting /></el-icon>
            应用管理
          </h1>
        </div>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加应用
        </el-button>
      </div>
    </el-header>

    <!-- 主要内容 -->
    <el-main class="main-content">
      <div class="content-wrapper">
        <!-- 搜索和筛选 -->
        <div class="toolbar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索应用..."
            class="search-input"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
            <el-option label="全部" value="" />
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </div>

        <!-- 应用列表 -->
        <el-table :data="filteredApps" v-loading="loading" stripe>
          <el-table-column prop="name" label="应用名称" min-width="150">
            <template #default="{ row }">
              <div class="app-name-cell">
                <el-avatar :size="32" :src="row.icon">
                  <el-icon><ChatDotRound /></el-icon>
                </el-avatar>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="category" label="分类" width="120">
            <template #default="{ row }">
              <el-tag size="small">{{ row.category }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="difyUrl" label="Dify URL" min-width="180" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ row.status === 'active' ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="editApp(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="text" size="small" @click="testApp(row)">
                <el-icon><Connection /></el-icon>
                测试
              </el-button>
              <el-button type="text" size="small" @click="deleteApp(row)" class="danger-btn">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-main>

    <!-- 创建/编辑应用对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      :title="editingApp ? '编辑应用' : '添加应用'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="appForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="appForm.name" placeholder="请输入应用名称" />
        </el-form-item>
        <el-form-item label="应用描述" prop="description">
          <el-input 
            v-model="appForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入应用描述"
          />
        </el-form-item>
        <el-form-item label="应用分类" prop="category">
          <el-select v-model="appForm.category" placeholder="请选择分类">
            <el-option label="通用助手" value="通用助手" />
            <el-option label="编程工具" value="编程工具" />
            <el-option label="写作工具" value="写作工具" />
            <el-option label="数据分析" value="数据分析" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="Dify URL" prop="difyUrl">
          <el-input 
            v-model="appForm.difyUrl" 
            placeholder="https://api.dify.ai 或您的私有部署地址"
          />
        </el-form-item>
        <el-form-item label="API Key" prop="apiKey">
          <el-input 
            v-model="appForm.apiKey" 
            type="password" 
            show-password
            placeholder="app-xxxxxxxxxx"
          />
        </el-form-item>
        <el-form-item label="应用图标">
          <el-upload
            :show-file-list="false"
            :before-upload="handleIconUpload"
            accept="image/*"
            class="icon-uploader"
          >
            <el-avatar v-if="appForm.icon" :size="60" :src="appForm.icon" />
            <el-icon v-else class="icon-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch 
            v-model="appForm.status" 
            active-value="active"
            inactive-value="disabled"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveApp" :loading="saving">
          {{ editingApp ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'AppManagement',
  setup() {
    const router = useRouter()
    const formRef = ref(null)
    
    const loading = ref(false)
    const saving = ref(false)
    const showCreateDialog = ref(false)
    const editingApp = ref(null)
    const searchKeyword = ref('')
    const statusFilter = ref('')
    
    const apps = ref([
      {
        id: 1,
        name: '智能助手',
        description: '通用AI助手，可以回答各种问题，协助完成日常任务',
        category: '通用助手',
        difyUrl: 'https://api.dify.ai',
        apiKey: 'app-xxx',
        icon: '',
        status: 'active',
        createdAt: new Date('2024-01-10')
      },
      {
        id: 2,
        name: '代码助手',
        description: '专业的编程助手，帮助编写、调试和优化代码',
        category: '编程工具',
        difyUrl: 'https://api.dify.ai',
        apiKey: 'app-yyy',
        icon: '',
        status: 'active',
        createdAt: new Date('2024-01-11')
      }
    ])

    const appForm = reactive({
      name: '',
      description: '',
      category: '',
      difyUrl: '',
      apiKey: '',
      icon: '',
      status: 'active'
    })

    const formRules = {
      name: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
      description: [{ required: true, message: '请输入应用描述', trigger: 'blur' }],
      category: [{ required: true, message: '请选择应用分类', trigger: 'change' }],
      difyUrl: [
        { required: true, message: '请输入Dify URL', trigger: 'blur' },
        { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
      ],
      apiKey: [{ required: true, message: '请输入API Key', trigger: 'blur' }]
    }

    // 过滤应用
    const filteredApps = computed(() => {
      let result = apps.value
      
      if (searchKeyword.value) {
        result = result.filter(app => 
          app.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
          app.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
      }
      
      if (statusFilter.value) {
        result = result.filter(app => app.status === statusFilter.value)
      }
      
      return result
    })

    // 格式化日期时间
    const formatDateTime = (date) => {
      return date.toLocaleString('zh-CN')
    }

    // 返回主页
    const goBack = () => {
      router.push('/')
    }

    // 编辑应用
    const editApp = (app) => {
      editingApp.value = app
      Object.assign(appForm, app)
      showCreateDialog.value = true
    }

    // 测试应用连接
    const testApp = async (app) => {
      ElMessage.info('正在测试连接...')
      try {
        // 模拟测试API连接
        await new Promise(resolve => setTimeout(resolve, 1000))
        ElMessage.success('连接测试成功')
      } catch (error) {
        ElMessage.error('连接测试失败')
      }
    }

    // 删除应用
    const deleteApp = async (app) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除应用 "${app.name}" 吗？此操作不可恢复。`,
          '确认删除',
          { type: 'warning' }
        )
        
        const index = apps.value.findIndex(a => a.id === app.id)
        if (index > -1) {
          apps.value.splice(index, 1)
          ElMessage.success('应用删除成功')
        }
      } catch {
        // 用户取消
      }
    }

    // 处理图标上传
    const handleIconUpload = (file) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        appForm.icon = e.target.result
      }
      reader.readAsDataURL(file)
      return false // 阻止自动上传
    }

    // 保存应用
    const saveApp = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        saving.value = true
        
        // 模拟保存
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        if (editingApp.value) {
          // 更新现有应用
          const index = apps.value.findIndex(a => a.id === editingApp.value.id)
          if (index > -1) {
            apps.value[index] = { ...apps.value[index], ...appForm }
          }
          ElMessage.success('应用更新成功')
        } else {
          // 创建新应用
          const newApp = {
            id: Date.now(),
            ...appForm,
            createdAt: new Date()
          }
          apps.value.push(newApp)
          ElMessage.success('应用创建成功')
        }
        
        showCreateDialog.value = false
        resetForm()
      } catch (error) {
        // 表单验证失败
      } finally {
        saving.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      editingApp.value = null
      Object.assign(appForm, {
        name: '',
        description: '',
        category: '',
        difyUrl: '',
        apiKey: '',
        icon: '',
        status: 'active'
      })
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }

    onMounted(() => {
      // 加载应用列表
    })

    return {
      formRef,
      loading,
      saving,
      showCreateDialog,
      editingApp,
      searchKeyword,
      statusFilter,
      apps,
      appForm,
      formRules,
      filteredApps,
      formatDateTime,
      goBack,
      editApp,
      testApp,
      deleteApp,
      handleIconUpload,
      saveApp,
      resetForm
    }
  }
}
</script>

<style scoped>
.management-container {
  min-height: 100vh;
  background: #f5f7fa;
}

.header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-btn {
  padding: 8px;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.main-content {
  padding: 20px;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.toolbar {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.search-input {
  width: 300px;
}

.app-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.danger-btn {
  color: #f56c6c;
}

.danger-btn:hover {
  color: #f56c6c;
}

.icon-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-uploader:hover {
  border-color: #409EFF;
}

.icon-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}
</style>
