<template>
  <div class="chat-container">
    <!-- 头部 -->
    <el-header class="chat-header">
      <div class="header-content">
        <div class="app-info">
          <el-button type="text" @click="goBack" class="back-btn">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
          <el-avatar :size="40" :src="currentApp?.icon">
            <el-icon><ChatDotRound /></el-icon>
          </el-avatar>
          <div class="app-details">
            <h3>{{ currentApp?.name || '加载中...' }}</h3>
            <span class="status">在线</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="text" @click="clearHistory">
            <el-icon><Delete /></el-icon>
            清空对话
          </el-button>
          <el-button type="text" @click="showSettings = true">
            <el-icon><Setting /></el-icon>
            设置
          </el-button>
        </div>
      </div>
    </el-header>

    <!-- 聊天消息区域 -->
    <el-main class="chat-main" ref="chatMainRef">
      <div class="messages-container" v-loading="loading">
        <div class="welcome-message" v-if="messages.length === 0">
          <el-icon size="60" color="#409EFF"><ChatDotRound /></el-icon>
          <h3>开始与 {{ currentApp?.name }} 对话</h3>
          <p>您可以问我任何问题，我会尽力为您提供帮助。</p>
        </div>

        <div 
          v-for="message in messages" 
          :key="message.id" 
          :class="['message', message.role]"
        >
          <div class="message-content">
            <div class="message-avatar">
              <el-avatar v-if="message.role === 'assistant'" :size="32" :src="currentApp?.icon">
                <el-icon><ChatDotRound /></el-icon>
              </el-avatar>
              <el-avatar v-else :size="32">
                <el-icon><User /></el-icon>
              </el-avatar>
            </div>
            <div class="message-body">
              <div class="message-text" v-html="formatMessage(message.content)"></div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>
        </div>

        <!-- 正在输入指示器 -->
        <div v-if="isTyping" class="message assistant">
          <div class="message-content">
            <div class="message-avatar">
              <el-avatar :size="32" :src="currentApp?.icon">
                <el-icon><ChatDotRound /></el-icon>
              </el-avatar>
            </div>
            <div class="message-body">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-main>

    <!-- 输入区域 -->
    <el-footer class="chat-footer">
      <div class="input-container">
        <div class="input-actions">
          <el-upload
            :show-file-list="false"
            :before-upload="handleFileUpload"
            accept="image/*,.pdf,.doc,.docx,.txt"
          >
            <el-button type="text" size="large">
              <el-icon><Paperclip /></el-icon>
            </el-button>
          </el-upload>
        </div>
        
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 4 }"
          placeholder="输入您的消息..."
          @keydown.enter.exact="handleSend"
          @keydown.enter.shift.exact.prevent
          class="message-input"
        />
        
        <el-button 
          type="primary" 
          @click="handleSend"
          :disabled="!inputMessage.trim() || isTyping"
          class="send-btn"
        >
          <el-icon><Promotion /></el-icon>
        </el-button>
      </div>
    </el-footer>

    <!-- 设置对话框 -->
    <el-dialog v-model="showSettings" title="聊天设置" width="400px">
      <el-form label-width="80px">
        <el-form-item label="模型温度">
          <el-slider v-model="settings.temperature" :min="0" :max="1" :step="0.1" />
        </el-form-item>
        <el-form-item label="最大长度">
          <el-input-number v-model="settings.maxTokens" :min="100" :max="4000" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'ChatInterface',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const chatMainRef = ref(null)
    
    const loading = ref(false)
    const isTyping = ref(false)
    const inputMessage = ref('')
    const showSettings = ref(false)
    
    const currentApp = ref(null)
    const messages = ref([])
    
    const settings = reactive({
      temperature: 0.7,
      maxTokens: 2000
    })

    // 模拟应用数据
    const mockApps = {
      1: { id: 1, name: '智能助手', icon: '', difyUrl: 'https://api.dify.ai', apiKey: 'app-xxx' },
      2: { id: 2, name: '代码助手', icon: '', difyUrl: 'https://api.dify.ai', apiKey: 'app-yyy' },
      3: { id: 3, name: '文档写作', icon: '', difyUrl: 'https://api.dify.ai', apiKey: 'app-zzz' }
    }

    // 加载应用信息
    const loadApp = async () => {
      const appId = parseInt(route.params.appId)
      currentApp.value = mockApps[appId]
      if (!currentApp.value) {
        ElMessage.error('应用不存在')
        router.push('/')
        return
      }
      
      // 加载历史消息
      await loadChatHistory()
    }

    // 加载聊天历史
    const loadChatHistory = async () => {
      loading.value = true
      try {
        // 模拟加载历史消息
        await new Promise(resolve => setTimeout(resolve, 500))
        messages.value = [
          {
            id: 1,
            role: 'assistant',
            content: '您好！我是您的AI助手，有什么可以帮助您的吗？',
            timestamp: new Date(Date.now() - 60000)
          }
        ]
      } catch (error) {
        ElMessage.error('加载聊天历史失败')
      } finally {
        loading.value = false
      }
    }

    // 发送消息
    const handleSend = async () => {
      if (!inputMessage.value.trim() || isTyping.value) return

      const userMessage = {
        id: Date.now(),
        role: 'user',
        content: inputMessage.value,
        timestamp: new Date()
      }

      messages.value.push(userMessage)
      const messageContent = inputMessage.value
      inputMessage.value = ''
      
      // 滚动到底部
      await nextTick()
      scrollToBottom()

      // 显示正在输入
      isTyping.value = true

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const assistantMessage = {
          id: Date.now() + 1,
          role: 'assistant',
          content: `我收到了您的消息："${messageContent}"。这是一个模拟回复，实际使用时会调用Dify API。`,
          timestamp: new Date()
        }

        messages.value.push(assistantMessage)
        await nextTick()
        scrollToBottom()
      } catch (error) {
        ElMessage.error('发送消息失败')
      } finally {
        isTyping.value = false
      }
    }

    // 处理文件上传
    const handleFileUpload = (file) => {
      ElMessage.info(`文件上传功能开发中: ${file.name}`)
      return false // 阻止自动上传
    }

    // 清空对话历史
    const clearHistory = async () => {
      try {
        await ElMessageBox.confirm('确定要清空所有对话记录吗？', '确认清空', {
          type: 'warning'
        })
        messages.value = []
        ElMessage.success('对话记录已清空')
      } catch {
        // 用户取消
      }
    }

    // 保存设置
    const saveSettings = () => {
      showSettings.value = false
      ElMessage.success('设置已保存')
    }

    // 返回应用列表
    const goBack = () => {
      router.push('/')
    }

    // 格式化消息内容
    const formatMessage = (content) => {
      return content.replace(/\n/g, '<br>')
    }

    // 格式化时间
    const formatTime = (date) => {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }

    // 滚动到底部
    const scrollToBottom = () => {
      if (chatMainRef.value) {
        const scrollElement = chatMainRef.value.$el
        scrollElement.scrollTop = scrollElement.scrollHeight
      }
    }

    // 监听消息变化，自动滚动
    watch(messages, () => {
      nextTick(() => {
        scrollToBottom()
      })
    }, { deep: true })

    onMounted(() => {
      loadApp()
    })

    return {
      chatMainRef,
      loading,
      isTyping,
      inputMessage,
      showSettings,
      currentApp,
      messages,
      settings,
      handleSend,
      handleFileUpload,
      clearHistory,
      saveSettings,
      goBack,
      formatMessage,
      formatTime
    }
  }
}
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.chat-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-btn {
  padding: 8px;
  margin-right: 8px;
}

.app-details h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.status {
  font-size: 12px;
  color: #67c23a;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.chat-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.messages-container {
  max-width: 800px;
  margin: 0 auto;
}

.welcome-message {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.welcome-message h3 {
  margin: 20px 0 10px;
  color: #303133;
}

.message {
  margin-bottom: 20px;
}

.message-content {
  display: flex;
  gap: 12px;
}

.message.user .message-content {
  flex-direction: row-reverse;
}

.message-body {
  max-width: 70%;
}

.message.user .message-body {
  text-align: right;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  line-height: 1.5;
}

.message.user .message-text {
  background: #409EFF;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 4px;
}

.typing-indicator {
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  display: flex;
  gap: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #c0c4cc;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.chat-footer {
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 16px 20px;
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.input-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-input {
  flex: 1;
}

.send-btn {
  height: 40px;
  width: 40px;
  border-radius: 50%;
}
</style>
